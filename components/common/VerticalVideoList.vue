<template>
  <ul class="list video">
    <!-- 如果有数据，显示数据 -->
    <template v-if="videoList && videoList.length > 0">
      <li v-for="(item, index) in videoList" :key="index">
        <navigator :url="`/pages/view/view?id=${item.id}`">
          <view class="cover">
            <image :src="(item.VideoPic || item.videoPic) || item.poster || 'https://placehold.co/300x400'" mode="aspectFill"/>
            <view class="video-info">
              <view class="pingfen" v-if="(item.VideoScore || item.videoScore) || item.score">
                {{ (item.VideoScore || item.videoScore) || (item.score ? item.score.toFixed(1) : '') }}
              </view>
              <view class="renqitime">
                <span>{{ formatHits((item.VideoHits || item.videoHits) || item.hits || 0) }}</span>
                <span>{{ (item.VideoDuration || item.videoDuration) || item.remarks || '' }}</span>
              </view>
            </view>
          </view>
          <view class="biaoti">{{ (item.VideoName || item.videoName) || item.title }}</view>
          <view class="tags" v-if="item.VideoTag || item.videoTag">
            {{ (item.VideoTag || item.videoTag).split(',').slice(0, 2).join(' ') }}
          </view>
        </navigator>
      </li>
    </template>
    <view class="no-data" v-if="videoList.length === 0">
      <text>暂无相关内容</text>
    </view>
  </ul>
</template>

<script>
export default {
  name: 'VerticalVideoList',
  props: {
    videoList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 格式化播放量
    formatHits(hits) {
      if (!hits) return '0';

      const num = parseInt(hits);
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toString();
    }
  }
}
</script>

<style scoped lang="scss">
.list {
  li {
    width: calc(33% - 11rpx);
  }
}

.list.video .cover{
  height: 40vw;
}
</style>
